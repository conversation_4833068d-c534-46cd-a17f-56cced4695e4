"""
JWT Configuration for QMigrator Web Agents Authentication.

This module contains JWT configuration settings and utilities.
"""

from config import Config


class JWTConfig:
    """
    JWT Configuration class that provides centralized access to JWT settings.
    
    This class reads JWT configuration from the main Config class and provides
    easy access to JWT-related settings throughout the authentication system.
    """
    
    @classmethod
    def get_secret_key(cls) -> str:
        """Get JWT secret key from configuration."""
        return Config.JWT_SECRET_KEY
    
    @classmethod
    def get_algorithm(cls) -> str:
        """Get JWT algorithm from configuration."""
        return Config.JWT_ALGORITHM
    
    @classmethod
    def is_auth_enabled(cls) -> bool:
        """Check if authentication is enabled."""
        return Config.AUTH_ENABLED
    
    @classmethod
    def get_public_routes(cls) -> list:
        """Get list of public routes that don't require authentication."""
        return Config.PUBLIC_ROUTES
    
    @classmethod
    def is_public_route(cls, path: str) -> bool:
        """Check if a given path is a public route."""
        return path in cls.get_public_routes()
