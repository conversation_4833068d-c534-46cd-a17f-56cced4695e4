from fastapi import HTTPException, status, Request
from starlette.responses import JSONResponse
import jwt
from starlette.middleware.base import BaseHTTPMiddleware

SECRET_KEY = "hfX.3zb3q3DYsN7o7.$z6u6IQ9BB7(@6"
ALGORITHM = "HS256"
PUBLIC_ROUTES = ['/ai/docs', '/ai/openapi.json', '/ai/redoc']

def verify_jwt_token(token: str):
    """
    Verifies the validity of a JWT token by decoding it using a secret key and specified algorithm.

    - The function attempts to decode the provided token. If the token is expired or invalid, 
    it raises an HTTPException with a 401 Unauthorized status and an appropriate error message.
    - If the token is valid, it returns the decoded payload.

    Args:
        - token (str): The JWT token to be verified.

    Returns:
        - dict: The decoded payload of the JWT token if valid.

    Raises:
        - HTTPException: If the token is expired or invalid, with a 401 Unauthorized status code 
        and an appropriate error message.
    """

    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # First, let's decode without verification to see the token contents
        unverified_payload = jwt.decode(token, options={"verify_signature": False})
        print(f"🔍 Token contents (unverified): {unverified_payload}")

        # Now try to verify with the secret key
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        print(f"✅ Token validated successfully: {payload}")
        return payload
    except jwt.ExpiredSignatureError:
        print("❌ Token has expired")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidSignatureError:
        print(f"❌ Invalid token signature - Current SECRET_KEY: {SECRET_KEY}")
        print("❌ The token was signed with a different secret key")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token signature - wrong secret key",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.PyJWTError as e:
        print(f"❌ JWT Error: {type(e).__name__}: {e}")
        raise credentials_exception
    
class JWTMiddleware(BaseHTTPMiddleware):
    """
    JWTMiddleware is a custom middleware that verifies the presence and validity of a JWT token in the 
    Authorization header of incoming HTTP requests, except for routes listed in PUBLIC_ROUTES.

    - If the request URL path is not in PUBLIC_ROUTES, the middleware checks for the presence of a 
    valid Authorization header containing a JWT token.
    - If the token is missing or invalid, an HTTP 401 Unauthorized response is raised.
    - If the token is valid, the request is passed on to the next middleware or route handler.

    Attributes:
        - PUBLIC_ROUTES (list): A list of paths that do not require authentication.

    Methods:
        - dispatch(request: Request, call_next): Handles the request and token verification logic. 
        If the token is valid, the request is passed on to the next middleware or route handler.
    """
    
    async def dispatch(self, request: Request, call_next):
        print(f"🔍 Request path: {request.url.path}")
        print(f"🔍 Public routes: {PUBLIC_ROUTES}")

        if request.url.path not in PUBLIC_ROUTES:
            print("🔒 Protected route - checking authentication")
            token = request.headers.get("Authorization")
            if token:
                print(f"🔍 Authorization header found: {token[:20]}...")
                token = token.split(" ")[1] if token.startswith("Bearer ") else token
                print(f"🔍 Extracted token: {token[:20]}...")
                try:
                    payload = verify_jwt_token(token)
                    print("✅ Token validation successful")
                    # Add user info to request state
                    request.state.user = payload
                except HTTPException as e:
                    print(f"❌ Token validation failed: {e.detail}")
                    return JSONResponse(
                        status_code=e.status_code,
                        content={"detail": e.detail},
                        headers=e.headers
                    )
            else:
                print("❌ No Authorization header found")
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Missing or invalid token"},
                    headers={"WWW-Authenticate": "Bearer"}
                )
        else:
            print("🔓 Public route - skipping authentication")

        response = await call_next(request)
        return response