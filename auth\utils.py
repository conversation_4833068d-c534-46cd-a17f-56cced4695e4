from fastapi import Request


def get_user_from_request(request: Request):
    return getattr(request.state, 'user', None)


def get_user_id_from_request(request: Request):
    user = get_user_from_request(request)
    return user.get('nameid') if user else None


def get_project_id_from_request(request: Request):
    user = get_user_from_request(request)
    return int(user.get('aud')) if user and user.get('aud') else None


def get_user_role_from_request(request: Request):
    user = get_user_from_request(request)
    return user.get('role') if user else None


def get_unique_name_from_request(request: Request):
    user = get_user_from_request(request)
    return user.get('unique_name') if user else None
