from fastapi import Request


def get_user_from_request(request: Request):
    return getattr(request.state, 'user', None)


def get_user_id_from_request(request: Request):
    user = get_user_from_request(request)
    return user.get('user_id') if user else None


def get_project_id_from_request(request: Request):
    user = get_user_from_request(request)
    return user.get('project_id') if user else None
