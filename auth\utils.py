"""
Authentication utilities for QMigrator Web Agents.

This module provides utility functions for working with authenticated requests
and extracting user information from JWT tokens.
"""

from fastapi import Request
from typing import Optional, Dict, Any


def get_user_from_request(request: Request) -> Optional[Dict[str, Any]]:
    """
    Extract user information from the request state.
    
    The JWT middleware adds decoded token payload to request.state.user
    when authentication is successful.
    
    Args:
        request (Request): FastAPI request object
        
    Returns:
        Optional[Dict[str, Any]]: User information from JWT token or None if not authenticated
    """
    return getattr(request.state, 'user', None)


def get_user_id_from_request(request: Request) -> Optional[str]:
    """
    Extract user ID from the request.
    
    Args:
        request (Request): FastAPI request object
        
    Returns:
        Optional[str]: User ID from JWT token or None if not available
    """
    user = get_user_from_request(request)
    return user.get('user_id') if user else None


def get_project_id_from_request(request: Request) -> Optional[int]:
    """
    Extract project ID from the request.
    
    Args:
        request (Request): FastAPI request object
        
    Returns:
        Optional[int]: Project ID from JWT token or None if not available
    """
    user = get_user_from_request(request)
    return user.get('project_id') if user else None


def is_authenticated(request: Request) -> bool:
    """
    Check if the request is authenticated.
    
    Args:
        request (Request): FastAPI request object
        
    Returns:
        bool: True if request has valid authentication, False otherwise
    """
    return get_user_from_request(request) is not None
