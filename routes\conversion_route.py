import uuid
from fastapi import APIRouter, HTTPException, Form, BackgroundTasks

# Import configuration and LLM components
from config import Config
from llm_config.config_manager import ConfigManager
from common.common import create_llm

# Import workflow components
from Conversion_Agent.workflow.graph_builder import GraphBuilder
from common.setup import Pre_SetupManager
from common.database import request_insert, request_update
from common.database import connect_database

router = APIRouter()


class ConversionData:
    def __init__(self, source_code, target_code, deployment_error, max_attempt_per_statement,
                 tgt_object_id, target_connection_id, migration_name, project_id, objectname,
                 cloud_category, run_number, target_schema_name, object_type):
        self.source_code = source_code
        self.target_code = target_code
        self.deployment_error = deployment_error
        self.max_attempt_per_statement = max_attempt_per_statement
        self.tgt_object_id = tgt_object_id
        self.target_connection_id = target_connection_id
        self.migration_name = migration_name
        self.project_id = project_id
        self.objectname = objectname
        self.cloud_category = cloud_category
        self.run_number = run_number
        self.target_schema_name = target_schema_name
        self.object_type = object_type


def run_conversion_background_task(request_data: ConversionData):
    """
    Background task function to run the conversion workflow.

    This function contains the complete conversion logic that runs in the background,
    allowing the API endpoint to return immediately while the conversion process continues.
    """
    try:
        print(f"🚀 Starting background conversion workflow...")

        # Pre-setup migration environment
        setup_manager = Pre_SetupManager(Config, request_data)
        print("🔧 Environment setup completed successfully.")

        # Get database names from environment variables (set by pre_setup_migration)
        source_db = Config.get_source_database()
        target_db = Config.get_target_database()
        print(f"📊 Source DB: {source_db} → Target DB: {target_db}")
        print(f"📁 Conversion files path: {setup_manager.conversion_path}")

        if setup_manager.target_db_credentials:
            print(f"🔐 Target DB credentials Fetched from API")
        else:
            print("⚠️ Target DB credentials not available")
            raise ValueError("Target DB credentials not available")

        if setup_manager.project_DB_details:
            print(f"🔐 Project DB credentials Fetched from API")
        else:
            print("⚠️ Project DB credentials not available")
            raise ValueError("Project DB credentials not available")

        # request insertion into DB
        prj_connection = connect_database(setup_manager.project_DB_details)
        request_id = request_insert(prj_connection, request_data.run_number, request_data.target_connection_id, 'Conversion-Agent','All',
                                    request_data.target_schema_name+'-'+ request_data.objectname, request_data.object_type)[0]

        # Setup the LLM
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        print(f"🔧 Initializing {llm_provider} LLM...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider}")

        # Initialize the workflow graph builder with the LLM
        graph_builder = GraphBuilder(llm)
        graph_builder.setup_graph()

        # Generate workflow visualization for debugging and documentation
        graph_builder.save_graph_image(graph_builder.graph)

        # Create a unique thread ID for this workflow execution
        thread_id = f"thread_{uuid.uuid4()}"
        print(f"🔗 Using thread ID: {thread_id}")

        # Execute the complete migration workflow with initial state
        graph_builder.invoke_graph({
            "source_code": request_data.source_code,
            "target_code": request_data.target_code,
            "deployment_error": request_data.deployment_error,
            "conversion_path": setup_manager.conversion_path,
            "target_db_credentials": setup_manager.target_db_credentials,
            "project_db_credentials": setup_manager.project_DB_details,
            "iteration_count": 1,
            "max_attempt_per_statement": request_data.max_attempt_per_statement,
            "migration_name": request_data.migration_name,
            "target_object_id": request_data.tgt_object_id,
            "object_type": request_data.object_type,
        }, thread_id=thread_id)

        print("🎉 Conversion workflow completed!")

        # Update the request status in the database
        request_update(prj_connection, request_id, 'Completed', None)

    except Exception as e:
        print(f"❌ Background conversion workflow failed: {str(e)}")
        # You might want to update the database with error status here
        # request_update(prj_connection, request_id, 'Failed', str(e))



@router.post("/conversion-agent")
def conversion_agent(
    background_tasks: BackgroundTasks,
    source_code: str = Form(..., description="Original source database code"),
    target_code: str = Form(..., description="Target database code with deployment errors"),
    deployment_error: str = Form(..., description="Error message from target database deployment attempt"),
    max_attempt_per_statement: int = Form(default=5, description="Maximum attempts per target statement"),
    tgt_object_id: int = Form(..., description="Target object ID for the migration"),
    target_connection_id: int = Form(..., description="Target database connection ID for the migration"),
    migration_name: str = Form(..., description="Name of the migration"),
    project_id: int = Form(..., description="Project ID for the migration"),
    objectname: str = Form(..., description="Name of the object being migrated"),
    cloud_category: str = Form(..., description="Cloud category for the migration"),
    run_number: int = Form(..., description="Run number for the migration"),
    target_schema_name: str = Form(..., description="Target schema name for the migration"),
    object_type: str = Form(..., description="Type of the object being migrated")
):
    """
    Convert database code using AI-driven migration workflow.

    This endpoint accepts form data instead of JSON, making it easier to handle
    large SQL content with control characters (newlines, tabs, etc.) without
    JSON encoding issues.

    The conversion process runs in the background, allowing this endpoint to return
    immediately while the conversion continues. Check the logs to track progress.
    """

    try:
        print(f"🚀 Starting conversion workflow in background...")

        # Create request object from form data
        request_data = ConversionData(
            source_code=source_code,
            target_code=target_code,
            deployment_error=deployment_error,
            max_attempt_per_statement=max_attempt_per_statement,
            tgt_object_id=tgt_object_id,
            target_connection_id=target_connection_id,
            migration_name=migration_name,
            project_id=project_id,
            objectname=objectname,
            cloud_category=cloud_category,
            run_number=run_number,
            target_schema_name=target_schema_name,
            object_type=object_type
        )

        # Add the conversion task to background tasks
        background_tasks.add_task(run_conversion_background_task, request_data)

        # Return immediately while the conversion runs in background
        return {
            "message": "Conversion Agent Process Started Please Track the Conversion Agent Status logs",
            "status": "started",
            "project_id": project_id,
            "tgt_object_id": tgt_object_id,
            "migration_name": migration_name,
            "note": "The conversion process is running in the background. Monitor the server logs for progress updates."
        }

    except Exception as e:
        print(f"❌ Failed to start conversion workflow: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to start conversion workflow",
                "message": str(e)
            }
        )