import os
from typing import Dict, Any, Literal
from langgraph.graph import <PERSON><PERSON><PERSON>h, END, START
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.runnables.graph import MermaidDrawMethod

# Local imports - State
from Conversion_Agent_Stage2.state import Stage2WorkflowState

# Local imports - Nodes
from Conversion_Agent_Stage2.nodes import Stage2ProcessingNodes


class Stage2GraphBuilder:
    """
    Stage 2 workflow graph builder for QMigrator module updates.

    This class builds and manages the LangGraph workflow for Stage 2 processing,
    providing a structured approach to updating QMigrator Python modules based
    on AI corrections from Stage 1.

    Key Features:
        - Post-Stage 1 processing for approved statement identification
        - Sequential statement-by-statement processing
        - Feature identification and validation
        - Module update generation and testing
        - Retry mechanism for failed operations
        - Comprehensive state management

    Workflow Architecture:
        Built using LangGraph for reliable state management and conditional
        routing based on validation results and processing outcomes.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 graph builder with AI language model integration.

        Sets up the graph builder with the provided language model for AI-driven
        analysis throughout the Stage 2 module update workflow.

        Args:
            llm: Initialized Language Model instance supporting structured outputs
                 for reliable AI analysis and module update operations. Compatible with
                 multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

        Attributes:
            llm: The language model instance used across all Stage 2 nodes
            builder: LangGraph StateGraph builder for workflow construction
            memory: MemorySaver for workflow state persistence and checkpointing
            nodes: Stage2ProcessingNodes instance for workflow operations
        """
        self.llm = llm
        self.builder = StateGraph(Stage2WorkflowState)
        self.memory = MemorySaver()
        self.nodes = Stage2ProcessingNodes(llm)

    def build_graph(self):
        """
        Build the Stage 2 workflow graph using LangGraph.

        Creates a simple linear workflow: START → Post-Stage 1 Processing → END

        Workflow Steps:
            1. START
            2. Post-Stage 1 Processing: Get approved statements and identify features
            3. END

        Returns:
            StateGraph: Configured workflow builder ready for compilation and execution
        """
        print("🔧 Building Stage 2 workflow graph...")

        # Create fresh builder instance to avoid cached nodes and ensure clean state
        self.builder = StateGraph(Stage2WorkflowState)

        # Add the single processing node
        self.builder.add_node("post_stage1_processing", self.nodes.post_stage1_processing)

        # Add edges: START → Post Processing → END
        self.builder.add_edge(START, "post_stage1_processing")
        self.builder.add_edge("post_stage1_processing", END)
        
        print("✅ Stage 2 workflow graph built successfully")
        return self.builder

    def setup_graph(self):
        """Setup and compile the Stage 2 workflow graph."""
        builder = self.build_graph()
        self.graph = builder.compile(
            interrupt_before=[], checkpointer=self.memory
        )
        return self.graph



    def save_graph_image(self, graph):
        """
        Save the workflow graph as a PNG image using Mermaid.

        Args:
            graph: Compiled LangGraph workflow
        """
        try:
            # Generate the PNG image
            img_data = graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API
            )

            # Save the image to a file
            graph_path = os.path.join("Conversion_Agent_Stage2", "stage2_workflow_graph.png")
            with open(graph_path, "wb") as f:
                f.write(img_data)

            print(f"Stage 2 graph image saved to {graph_path}")
        except Exception as e:
            print(f"Warning: Could not generate Stage 2 graph image: {str(e)}")
            print("Continuing execution without graph visualization...")