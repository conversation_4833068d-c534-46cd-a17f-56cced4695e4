SET search_path TO LAB;

CREATE OR R<PERSON>LACE PROCEDURE lab.P_ADDMICRO<PERSON><PERSON>OGYPROCESS (ICLOB_MICROBI<PERSON>OGYPROCESS IN text, ON_RETURNCODE INOUT numeric)
LANGUAGE plpgsql
SECURITY DEFINER
AS $BODY$
DECLARE
    V_MICRO<PERSON>OLOGYPROCESS xml;
    V_INDEX xml;
    V_PROCESSINGNO numeric;
    V_LOGINID varchar(50);
    V_REQUESTTESTID lab.REQUESTTESTS.REQUESTTESTID%TYPE;
    LV_REQUESTTESTID numeric;
    V_COMPLETED numeric(2);
    V_LRN lab.REQUESTTESTS.LRN%TYPE;
    V_TOTALCOUNT numeric(10);
    V_TOTALVERIFIEDCOUNT numeric(10);
    V_SAMPLEVERIFY numeric(10);
    V_SAMPLEVERIFYCOMMENT varchar(50);
    V_ORGANISMMAPPINGID numeric(10);
    V_RESULT numeric(10);
    I numeric(10);
    V_SECONDOPINIONCOMMENT varchar(50);
    V_SECONDOPINION numeric;
    V_ORGANISMMETHODOLOGYID numeric;
    v_LocationID numeric;  
    V_PREVIEWFLAG numeric;
    lv_antibioticresult varchar(100);
    V_RESULTS varchar(50);
    --, '1234567890/')
BEGIN
    SET search_path TO LAB;

    /*
INSERT INTO lab.TEMP (A) VALUES (ICLOB_MICROBIOLOGYPROCESS);
     COMMIT;
     */
    V_MICROBIOLOGYPROCESS := xml(ICLOB_MICROBIOLOGYPROCESS);
    V_SECONDOPINION := (
        CASE WHEN (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@Secondopinion', V_MICROBIOLOGYPROCESS)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@Secondopinion', V_MICROBIOLOGYPROCESS)))::text
        END)::numeric;
    BEGIN
        V_SECONDOPINIONCOMMENT := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@SecondopinionComment', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@SecondopinionComment', V_MICROBIOLOGYPROCESS)))
            END)::text;
    EXCEPTION
        WHEN OTHERS THEN
            V_SECONDOPINIONCOMMENT := NULL;
    END;
    V_LOGINID := (
        CASE WHEN (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/LoginID/text()', V_MICROBIOLOGYPROCESS)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('/ProcessSampleMicroRequest/LoginID/text()', V_MICROBIOLOGYPROCESS)))
        END)::text;
V_REQUESTTESTID := (
    CASE WHEN (
        SELECT
            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@RequestTestID', V_MICROBIOLOGYPROCESS)))::text = '' THEN
        NULL
    ELSE
        (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@RequestTestID', V_MICROBIOLOGYPROCESS)))::text
    END)::numeric;
V_PREVIEWFLAG := (
    CASE WHEN (
        SELECT
            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@Count', V_MICROBIOLOGYPROCESS)))::text = '' THEN
        NULL
    ELSE
        (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@Count', V_MICROBIOLOGYPROCESS)))::text
    END)::numeric;
LV_REQUESTTESTID := V_REQUESTTESTID;
V_COMPLETED := (
    CASE WHEN (
        SELECT
            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@ProcessCompleted', V_MICROBIOLOGYPROCESS)))::text = '' THEN
        NULL
    ELSE
        (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@ProcessCompleted', V_MICROBIOLOGYPROCESS)))::text
    END)::numeric;
V_SAMPLEVERIFY := (
    CASE WHEN (
        SELECT
            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@SampleVerify', V_MICROBIOLOGYPROCESS)))::text = '' THEN
        NULL
    ELSE
        (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@SampleVerify', V_MICROBIOLOGYPROCESS)))::text
    END)::numeric;
V_ORGANISMMETHODOLOGYID := (
    CASE WHEN (
        SELECT
            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@OrganismMethodologyID', V_MICROBIOLOGYPROCESS)))::text = '' THEN
        NULL
    ELSE
        (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@OrganismMethodologyID', V_MICROBIOLOGYPROCESS)))::text
    END)::numeric;
BEGIN
    V_SAMPLEVERIFYCOMMENT := (
        CASE WHEN (
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@Verifycomments', V_MICROBIOLOGYPROCESS)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@Verifycomments', V_MICROBIOLOGYPROCESS)))
        END)::text;
EXCEPTION
    WHEN OTHERS THEN
        V_SAMPLEVERIFYCOMMENT := NULL;
END;
SELECT
    rt.locationid INTO STRICT v_LocationID
FROM
    lab.requesttests rt
WHERE
    rt.requesttestid = V_REQUESTTESTID;
    IF V_SAMPLEVERIFY = 1 THEN
        SELECT
            MAX(MBPR.PROCESSINGNO) INTO STRICT V_PROCESSINGNO
        FROM
            lab.MICROBIOLOGYPROCESSING MBPR
        WHERE
            MBPR.REQUESTTESTID = LV_REQUESTTESTID;
            I := 1;
            LOOP
                SELECT
                    (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/AntibioticDetails[' || I || ']', V_MICROBIOLOGYPROCESS))) INTO V_INDEX;
                    EXIT
    WHEN V_INDEX IS NULL;
        lv_antibioticresult := (
            CASE WHEN (
                SELECT
                    unnest(xpath('/AntibioticDetails/@AntibioticResult', V_INDEX)))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/AntibioticDetails/@AntibioticResult', V_INDEX)))
            END)::text;
    V_ORGANISMMAPPINGID := (
        CASE WHEN (
            SELECT
                unnest(xpath('/AntibioticDetails/@OrganismMappingID', V_INDEX)))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('/AntibioticDetails/@OrganismMappingID', V_INDEX)))::text
        END)::numeric;
V_RESULT := REPLACE(lv_antibioticresult, RTRIM(lv_antibioticresult, '1234567890/'));
V_RESULTS := (
    CASE WHEN (
        SELECT
            unnest(xpath('/AntibioticDetails/@AntibioticResults', V_INDEX)))::text = '' THEN
        NULL
    ELSE
        (
            SELECT
                unnest(xpath('/AntibioticDetails/@AntibioticResults', V_INDEX)))::text
    END)::numeric;
UPDATE
    lab.MICROBIOLOGYRESULTS MBR
SET
    MBR.RESULT = V_RESULT,
    MBR.RESULTS = (
        CASE WHEN lv_antibioticresult IS NOT NULL
            AND V_RESULTS IS NULL THEN
            F_GETMICROBIOLOGYRESULT (lv_antibioticresult, F_GETLOVMEANING (V_ORGANISMMETHODOLOGYID, 'OrganismMethodology'), (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/AntibioticDetails/@OrganismMappingID', V_INDEX)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/AntibioticDetails/@OrganismMappingID', V_INDEX)))
                    END)::text::numeric, v_LocationID)
        ELSE
            --to_char(v_result)
            V_RESULTS::varchar
        END),
MBR.CREATEDDATE = current_timestamp(0)::timestamp,
MBR.CREATEDBY = V_LOGINID
WHERE
    MBR.PROCESSINGNO = V_PROCESSINGNO
    AND MBR.ORGANISMMAPPINGID = V_ORGANISMMAPPINGID;
    I := I + 1;
                END LOOP;
                IF (V_SECONDOPINION = 0) THEN
                    UPDATE
                        lab.MICROBIOLOGYPROCESSING MP
                    SET
                        COMMENTS = V_SAMPLEVERIFYCOMMENT
                    WHERE
                        MP.PROCESSINGNO = V_PROCESSINGNO;
                        IF V_PREVIEWFLAG = 2 THEN
                            UPDATE
                                lab.REQUESTTESTS
                            SET
                                SAMPLEVERIFY = 'Y',
                                UPDATEDBY = V_LOGINID,
                                UPDATEDDATE = current_timestamp(0)::timestamp,
                                VERIFYTIME = current_timestamp(0)::timestamp
                            WHERE
                                REQUESTTESTID = V_REQUESTTESTID;
                            END IF;
                            UPDATE
                                lab.LABREPORTS
                            SET
                                SECONDOPINION = V_SAMPLEVERIFYCOMMENT,
                                UPDATEDBY = V_LOGINID,
                                UPDATEDDATE = current_timestamp(0)::timestamp
                            WHERE
                                REQUESTTESTID = LV_REQUESTTESTID;
                            ELSE
                                UPDATE
                                    lab.MICROBIOLOGYPROCESSING MP
                                SET
                                    SECONDOPINION = V_SECONDOPINIONCOMMENT
                                WHERE
                                    MP.PROCESSINGNO = V_PROCESSINGNO;
                                    UPDATE
                                        lab.REQUESTTESTS
                                    SET
                                        SAMPLEVERIFY = 'S',
                                        UPDATEDBY = V_LOGINID,
                                        UPDATEDDATE = current_timestamp(0)::timestamp
                                    WHERE
                                        REQUESTTESTID = V_REQUESTTESTID;
                                        UPDATE
                                            lab.LABREPORTS
                                        SET
                                            SECONDOPINION = V_SECONDOPINIONCOMMENT,
                                            UPDATEDBY = V_LOGINID,
                                            UPDATEDDATE = current_timestamp(0)::timestamp
                                        WHERE
                                            REQUESTTESTID = LV_REQUESTTESTID;
                    END IF;
                ELSE
                    SELECT
                        nextval('lab.S_SPECIMENID') INTO STRICT V_PROCESSINGNO;
        INSERT INTO lab.MICROBIOLOGYPROCESSING (PROCESSINGNO, REQUESTTESTID, METHODOLOGYID, INOCULATETIME, INCUBATIONTIME, TEMPERATURE, TEMPTYPE, COLONOYSIZE, ORGANISMID, GROWTHTYPE, STATUS, CREATEDBY, CREATEDDATE, COMMENTS, ORGANISMTYPEID)
            VALUES (V_PROCESSINGNO, V_REQUESTTESTID, (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@OrganismMethodologyID', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@OrganismMethodologyID', V_MICROBIOLOGYPROCESS)))
                    END)::text::numeric,
                TO_DATE((
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@InocolateTime', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@InocolateTime', V_MICROBIOLOGYPROCESS)))
                    END)::text, 'MM-DD-YYYY HH24:MI:SS'),
                (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@IncubationTime', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@IncubationTime', V_MICROBIOLOGYPROCESS)))
                    END)::text,
                (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@IncubationTemperature', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@IncubationTemperature', V_MICROBIOLOGYPROCESS)))
                    END)::text,
                (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@IncubationTemperatureType', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@IncubationTemperatureType', V_MICROBIOLOGYPROCESS)))
                    END)::text::numeric,
                REPLACE(REPLACE(REPLACE(REPLACE(REPLACE((
                                    CASE WHEN (
                                        SELECT
                                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@ColonySize', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                                        NULL
                                    ELSE
                                        (
                                            SELECT
                                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/@ColonySize', V_MICROBIOLOGYPROCESS)))
                                    END)::text, '>', '>'), '<', '<'), '&amp;', '&;'), '$quot;', '";'), '&apos;', ''';'),(case when(select unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/@OrganismID',V_MICROBIOLOGYPROCESS)))::text='' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/@OrganismID', V_MICROBIOLOGYPROCESS)))
END)::text::numeric,
                (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/@GrowthType', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/@GrowthType', V_MICROBIOLOGYPROCESS)))
                    END)::text,
                1,
                V_LOGINID,
                current_timestamp(0)::timestamp,
                V_SAMPLEVERIFYCOMMENT,
                (
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/@OrganismTypeID', V_MICROBIOLOGYPROCESS)))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/@OrganismTypeID', V_MICROBIOLOGYPROCESS)))
                    END)::text);

INSERT INTO lab.MICROBIOLOGYRESULTS (PROCESSINGNO, ORGANISMMAPPINGID, RESULT, STATUS, CREATEDBY, CREATEDDATE, RESULTS)
SELECT
    V_PROCESSINGNO,
    (
        CASE WHEN (
            SELECT
                unnest(xpath('/AntibioticDetails/@OrganismMappingID', VALUE (LI))))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('/AntibioticDetails/@OrganismMappingID', VALUE (LI))))
        END)::text::numeric,
REPLACE(REPLACE(REPLACE(REPLACE(REPLACE((
                    CASE WHEN (
                        SELECT
                            unnest(xpath('/AntibioticDetails/@AntibioticResult', VALUE (LI))))::text = '' THEN
                        NULL
                    ELSE
                        (
                            SELECT
                                unnest(xpath('/AntibioticDetails/@AntibioticResult', VALUE (LI))))
                    END)::text, '>', '>'), '<', '<'), '&amp;', '&;'), '$quot;', '";'), '&apos;', ''';'),
1,
V_LOGINID,
current_timestamp(0)::timestamp,
(
    CASE WHEN (
        CASE WHEN (
            SELECT
                unnest(xpath('/AntibioticDetails/@AntibioticResult', VALUE (LI))))::text = '' THEN
            NULL
        ELSE
            (
                SELECT
                    unnest(xpath('/AntibioticDetails/@AntibioticResult', VALUE (LI))))
        END)::text IS NOT NULL
        AND (
            CASE WHEN (
                SELECT
                    unnest(xpath('/AntibioticDetails/@AntibioticResults', VALUE (LI))))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/AntibioticDetails/@AntibioticResults', VALUE (LI))))
            END)::text IS NULL THEN
        F_GETMICROBIOLOGYRESULT ((
            CASE WHEN (
                SELECT
                    unnest(xpath('/AntibioticDetails/@AntibioticResult', VALUE (LI))))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/AntibioticDetails/@AntibioticResult', VALUE (LI))))
            END)::text, F_GETLOVMEANING (V_ORGANISMMETHODOLOGYID, 'OrganismMethodology'), (
            CASE WHEN (
                SELECT
                    unnest(xpath('/AntibioticDetails/@OrganismMappingID', VALUE (LI))))::text = '' THEN
                NULL
            ELSE
                (
                    SELECT
                        unnest(xpath('/AntibioticDetails/@OrganismMappingID', VALUE (LI))))
            END)::text::numeric, v_LocationID)
    ELSE
        REPLACE(REPLACE(REPLACE(REPLACE(REPLACE((
                            CASE WHEN (
                                SELECT
                                    unnest(xpath('/AntibioticDetails/@AntibioticResults', VALUE (LI))))::text = '' THEN
                                NULL
                            ELSE
                                (
                                    SELECT
                                        unnest(xpath('/AntibioticDetails/@AntibioticResults', VALUE (LI))))
                            END)::text, '>', '>'), '<', '<'), '&amp;', '&;'), '$quot;', '";'), '&apos;', ''';')
    END)
FROM
    TABLE (XMLSEQUENCE ((
            SELECT
                unnest(xpath('/ProcessSampleMicroRequest/OrganismDetails/OrganismType/AntibioticDetails', V_MICROBIOLOGYPROCESS)))::text)) LI;

IF V_COMPLETED = 1 THEN
    UPDATE
        lab.REQUESTTESTS
    SET
        PROCESSING = 'Y',
        SAMPLEVERIFY = 'N',
        SAMPLEPROCESSTIME = current_timestamp(0)::timestamp,
        UPDATEDBY = V_LOGINID
    WHERE
        REQUESTTESTID = V_REQUESTTESTID;

    END IF;

END IF;

SELECT
    LRN INTO STRICT V_LRN
FROM
    lab.REQUESTTESTS
WHERE
    REQUESTTESTID = V_REQUESTTESTID;

    SELECT
        COUNT(REQUESTTESTID) INTO STRICT V_TOTALCOUNT
    FROM
        lab.REQUESTTESTS
    WHERE
        TESTSTATUS = 1
        AND LRN = V_LRN;

        SELECT
            COUNT(REQUESTTESTID) INTO STRICT V_TOTALVERIFIEDCOUNT
        FROM
            lab.REQUESTTESTS A
        WHERE
            TESTSTATUS = 1
            AND A.SAMPLEVERIFY = 'Y'
            AND LRN = V_LRN;

            ON_RETURNCODE := 0;

            --in progress
            IF V_TOTALVERIFIEDCOUNT = V_TOTALCOUNT THEN
                UPDATE
                    lab.RAISEREQUEST RR
                SET
                    REQUESTSTATUS = 'C',
                    UPDATEDBY = V_LOGINID,
                    UPDATEDDATE = current_timestamp(0)::timestamp
                WHERE
                    RR.LRN = V_LRN;

                    ON_RETURNCODE := 1;

                    --completed.
                END IF;

END;

$BODY$;

