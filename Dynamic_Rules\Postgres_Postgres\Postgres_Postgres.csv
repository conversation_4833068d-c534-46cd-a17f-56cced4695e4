Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
Postgres_Postgres,Start:Select End:;&Start:insert End:;,Start:/* End:*/&Start:-- End:\n,,Query,ObjectTypes object (898)
Postgres_Postgres,"Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1
",,,Index,ObjectTypes object (916)
Postgres_Postgres,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (868)
Postgres_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (919)
Postgres_Postgres,Start:create table End:object-1&Start:create table End:;,,,Partition,ObjectTypes object (880)
Postgres_Postgres,Start:Create or replace synonym End:object-1&Start:Create or replace synonym End:;&Start:Create synonym End:object-1&Start:Create synonym End:;,,,Synonym,ObjectTypes object (901)
Postgres_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (877)
Postgres_Postgres,Start: Create or replace trigger End:Object-1&Start: Create or replace editionable trigger End:Object-1&Start: Create trigger End:Object-1,Start:/* End:*/&Start:-- End:\n,,Trigger,ObjectTypes object (904)
Postgres_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (922)
Postgres_Postgres,Start: Create or replace procedure End:Object-1&Start: Create or replace editionable procedure End:Object-1&Start: Create procedure End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (859)
Postgres_Postgres,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (862)
Postgres_Postgres,Start: Create sequence End:Object-1&Start: Create sequence End:;,,,Sequence,ObjectTypes object (883)
Postgres_Postgres,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (910)
Postgres_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (925)
Postgres_Postgres,Start:create table End:;&Start:create global temporary table End:;&Start:create table End:object-1,Start:/* End:*/&Start:-- End:\n,,Temporary_Table,ObjectTypes object (928)
Postgres_Postgres,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (865)
Postgres_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (889)
Postgres_Postgres,Start: Create or replace type End:;&Start: Create or replace type End:Object-1,"Start:/* End:*/&Start:-- End:\n
",,Type,ObjectTypes object (913)
