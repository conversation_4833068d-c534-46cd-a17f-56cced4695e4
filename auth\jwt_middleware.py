from fastapi import HTT<PERSON><PERSON>x<PERSON>, status, Request
from starlette.responses import JSONResponse
import jwt
from starlette.middleware.base import BaseHTTPMiddleware
from .jwt_config import SECRET_KEY, ALGORITHM, PUBLIC_ROUTES


def verify_jwt_token(token: str):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.PyJWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


class JWTMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Skip authentication for public routes
        if request.url.path in PUBLIC_ROUTES:
            response = await call_next(request)
            return response

        # Get Authorization header
        token = request.headers.get("Authorization")
        if not token:
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Missing or invalid token"},
                headers={"WWW-Authenticate": "Bearer"}
            )

        # Extract token from "Bearer <token>"
        if token.startswith("Bearer "):
            token = token.split(" ")[1]

        try:
            # Verify JWT token
            payload = verify_jwt_token(token)
            # Add user info to request state
            request.state.user = payload
        except HTTPException as e:
            return JSONResponse(
                status_code=e.status_code,
                content={"detail": e.detail},
                headers=e.headers or {}
            )

        # Proceed to next handler
        response = await call_next(request)
        return response
