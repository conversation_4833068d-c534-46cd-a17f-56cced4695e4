"""
JWT Middleware for QMigrator Web Agents Authentication.

This module provides JWT-based authentication middleware that validates Bearer tokens
for all routes except those listed in PUBLIC_ROUTES.
"""

from fastapi import HTTPException, status, Request
from starlette.responses import JSONResponse
import jwt
from starlette.middleware.base import BaseHTTPMiddleware
from .jwt_config import J<PERSON><PERSON>onfig


def verify_jwt_token(token: str):
    """
    Verifies the validity of a JWT token by decoding it using a secret key and specified algorithm.

    - The function attempts to decode the provided token. If the token is expired or invalid, 
    it raises an HTTPException with a 401 Unauthorized status and an appropriate error message.
    - If the token is valid, it returns the decoded payload.

    Args:
        - token (str): The JWT token to be verified.

    Returns:
        - dict: The decoded payload of the JWT token if valid.

    Raises:
        - HTTPException: If the token is expired or invalid, with a 401 Unauthorized status code 
        and an appropriate error message.
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(
            token, 
            JWTConfig.get_secret_key(), 
            algorithms=[JWTConfig.get_algorithm()]
        )
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.PyJWTError:
        raise credentials_exception


class JWTMiddleware(BaseHTTPMiddleware):
    """
    JWTMiddleware is a custom middleware that verifies the presence and validity of a JWT token in the 
    Authorization header of incoming HTTP requests, except for routes listed in PUBLIC_ROUTES.

    - If the request URL path is not in PUBLIC_ROUTES, the middleware checks for the presence of a 
    valid Authorization header containing a JWT token.
    - If the token is missing or invalid, an HTTP 401 Unauthorized response is raised.
    - If the token is valid, the request is passed on to the next middleware or route handler.
    - Authentication can be disabled by setting AUTH_ENABLED=false in configuration.

    Methods:
        - dispatch(request: Request, call_next): Handles the request and token verification logic. 
        If the token is valid, the request is passed on to the next middleware or route handler.
    """
    
    async def dispatch(self, request: Request, call_next):
        """
        Process the incoming request and validate JWT token if required.

        Args:
            request (Request): The incoming HTTP request
            call_next: The next middleware or route handler in the chain

        Returns:
            Response: Either an error response for authentication failures or
                     the response from the next handler for valid requests
        """
        # Skip authentication for public routes only
        if JWTConfig.is_public_route(request.url.path):
            response = await call_next(request)
            return response

        # Skip authentication ONLY if explicitly disabled via environment variable
        # This should only be used for development/testing purposes
        if not JWTConfig.is_auth_enabled():
            print("⚠️  WARNING: Authentication is disabled via AUTH_ENABLED=false")
            response = await call_next(request)
            return response
        
        # Check for Authorization header
        token = request.headers.get("Authorization")
        if token:
            # Extract token from "Bearer <token>" format
            if token.startswith("Bearer "):
                token = token.split(" ")[1]
            else:
                # Handle case where token doesn't have "Bearer " prefix
                pass
            
            try:
                # Verify the JWT token
                payload = verify_jwt_token(token)
                # Add user info to request state for use in route handlers
                request.state.user = payload
            except HTTPException as e:
                return JSONResponse(
                    status_code=e.status_code,
                    content={"detail": e.detail},
                    headers=e.headers if e.headers else {}
                )
        else:
            # No Authorization header found
            return JSONResponse(
                status_code=status.HTTP_401_UNAUTHORIZED,
                content={"detail": "Missing or invalid token"},
                headers={"WWW-Authenticate": "Bearer"}
            )
        
        # Token is valid, proceed to next handler
        response = await call_next(request)
        return response
