"""
Stage 2 Workflow State Management for QMigrator Module Updates.

This module defines the state management for Stage 2 processing, which handles
AI corrections from Stage 1 to update QMigrator Python modules through a
workflow with retry mechanism.
"""

from typing import List, Optional
from pydantic import BaseModel, Field


class Stage2WorkflowState(BaseModel):
    """
    Workflow state for Stage 2 QMigrator module update processing.

    Simplified state for Post-Stage 1 Processing node only.
    """

    # Input parameters
    migration_name: str = Field(
        default="Oracle_Postgres14",
        description="Migration name (e.g., Oracle_Postgres14)"
    )
    target_object_id: int = Field(description="Target object ID from Stage 1")

    # Post-Stage 1 Processing results
    approved_statements: Optional[List] = Field(
        default=None,
        description="List of approved statements from Stage 1"
    )