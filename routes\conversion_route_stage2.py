import uuid
from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Form, BackgroundTasks

# Import configuration and LLM components
from llm_config.config_manager import ConfigManager
from common.common import create_llm

# Import Stage 2 workflow components
from Conversion_Agent_Stage2.workflow.graph_builder import Stage2GraphBuilder

router = APIRouter()


class Stage2ConversionData:
    """Data class for Stage 2 conversion request parameters."""
    def __init__(self, tgt_object_id, migration_name, project_id, target_connection_id,
                 objectname, cloud_category, run_number, target_schema_name, object_type):
        self.tgt_object_id = tgt_object_id
        self.migration_name = migration_name
        self.project_id = project_id
        self.target_connection_id = target_connection_id
        self.objectname = objectname
        self.cloud_category = cloud_category
        self.run_number = run_number
        self.target_schema_name = target_schema_name
        self.object_type = object_type


def run_stage2_conversion_background_task(request_data: Stage2ConversionData):
    """
    Background task for Stage 2 conversion workflow.

    This function contains the complete Stage 2 conversion logic that runs in the background,
    allowing the API endpoint to return immediately while the Stage 2 process continues.
    """
    try:
        print(f"🚀 Starting Stage 2 background conversion workflow...")

        # TODO: Pre-setup migration environment when needed
        print("🔧 Stage 2 environment setup - TODO: Implement when required")

        # TODO: Add database credential validation when needed
        print("🔐 Database credentials setup - TODO: Implement when required")

        # Initialize LLM configuration
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        print(f"🔧 Initializing {llm_provider} LLM for Stage 2...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} for Stage 2 processing")

        # Initialize the Stage 2 workflow graph builder with the LLM
        graph_builder = Stage2GraphBuilder(llm)
        graph_builder.setup_graph()

        # Generate workflow visualization for debugging and documentation
        graph_builder.save_graph_image(graph_builder.graph)

        # Generate unique thread ID for this Stage 2 workflow
        thread_id = str(uuid.uuid4())
        print(f"🧵 Stage 2 workflow thread ID: {thread_id}")

        # Execute the Stage 2 workflow with initial state
        print("🔄 Starting Stage 2 workflow execution...")
        result = graph_builder.graph.invoke({
            "target_object_id": request_data.tgt_object_id,
            "migration_name": request_data.migration_name
        }, config={"thread_id": thread_id})

        print("✅ Stage 2 workflow completed successfully")
        print(f"📊 Final result: {result}")

    except Exception as e:
        print(f"❌ Stage 2 background task failed: {str(e)}")
        raise


@router.post("/conversion-agent-stage2")
def conversion_agent_stage2(
    background_tasks: BackgroundTasks,
    tgt_object_id: int = Form(..., description="Target object ID from Stage 1 processing"),
    migration_name: str = Form(default="Oracle_Postgres14", description="Name of the migration"),
    project_id: int = Form(..., description="Project ID for the migration"),
    target_connection_id: int = Form(..., description="Target database connection ID"),
    objectname: str = Form(..., description="Name of the object being processed"),
    cloud_category: str = Form(..., description="Cloud category for the migration"),
    run_number: int = Form(..., description="Run number for the migration"),
    target_schema_name: str = Form(..., description="Target schema name"),
    object_type: str = Form(..., description="Type of the object being processed")
):
    """
    Stage 2 Conversion Agent - QMigrator Module Updates.

    This endpoint processes AI corrections from Stage 1 to update QMigrator Python modules
    through a workflow with retry mechanism, ensuring future conversions work without AI intervention.

    The Stage 2 process:
    1. Gets all approved statements from Stage 1 (is_manually_deployed = true)
    2. Runs QMigrator object-level conversion to identify feature combinations
    3. Stores metadata in qm_conversion_stage2 table

    The conversion process runs in the background, allowing this endpoint to return
    immediately while Stage 2 processing continues. Check the logs to track progress.
    """

    try:
        print(f"🚀 Starting Stage 2 conversion workflow in background...")

        # Create Stage 2 request object from form data
        request_data = Stage2ConversionData(
            tgt_object_id=tgt_object_id,
            migration_name=migration_name,
            project_id=project_id,
            target_connection_id=target_connection_id,
            objectname=objectname,
            cloud_category=cloud_category,
            run_number=run_number,
            target_schema_name=target_schema_name,
            object_type=object_type
        )

        # Add the Stage 2 conversion task to background tasks
        background_tasks.add_task(run_stage2_conversion_background_task, request_data)

        # Return immediately while Stage 2 conversion runs in background
        return {
            "message": "Stage 2 Conversion Agent Process Started - Please Track the Stage 2 Conversion Agent Status logs",
            "status": "started",
            "stage": "Stage 2 - QMigrator Module Updates",
            "project_id": project_id,
            "tgt_object_id": tgt_object_id,
            "migration_name": migration_name,
            "note": "The Stage 2 conversion process is running in the background. Monitor the server logs for progress updates."
        }

    except Exception as e:
        print(f"❌ Failed to start Stage 2 conversion workflow: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to start Stage 2 conversion workflow",
                "message": str(e),
                "stage": "Stage 2 - QMigrator Module Updates"
            }
        )