# Stage 2 LangGraph Application - Complete Implementation Plan

## Overview
Stage 2 processes AI-corrected statements from Stage 1 to identify which QMigrator Python modules need updating. The workflow validates responsible features through an 8-step sequential process with retry mechanisms for failed validations.

## Workflow Diagram
![Stage 2 Workflow Diagram](Stage2_diagram2.png)

*Figure 1: Complete Stage 2 LangGraph workflow showing all 8 steps with decision points and retry mechanisms*

---

## Goal & Context

### The Problem
- QMigrator converts source code to target code using Python modules
- The converted code has deployment issues (errors when deployed)
- Stage 1 uses AI to fix these deployment errors and produces corrected statements
- Stage 2 needs to update QMigrator's Python modules so they produce the corrected output directly

### The Solution
Update QMigrator's conversion modules based on AI corrections, so future conversions work correctly without needing AI intervention.

---

## Stage 1: Database Changes for Stage 2 Process
- `conversion_agent_deployment` table requires adding `source_statement_number` column
- Need to keep deployment error in table even if it's successful

---

## Process Inputs
**Input Parameters:** `run_no`, `target_object_id`, `objecttype`, `object_name`, `migration_name`, `source_code`, `target_code`

---

## Step 1: Post-Stage 1 Processing

### Purpose
Get all approved AI corrections and identify which QMigrator features were used for this object.

### Process
1. **Run QMigrator object-level conversion** once for the whole object
2. **Store metadata** for each source statement with identified feature combinations

### Database Schema

#### qm_conversion_stage2 (Parent Table)
```sql
CREATE TABLE IF NOT EXISTS public.qm_conversion_stage2 (
    stage2_id BIGSERIAL PRIMARY KEY,
    tgt_object_id BIGINT,
    migration_name VARCHAR(100),
    source_statement_id BIGINT,
    original_source_statement TEXT,
    pre_conversion_source TEXT,
    target_statement TEXT,
    created_dt TIMESTAMP DEFAULT NOW(),
    updated_dt TIMESTAMP NULL
);
```

#### qm_conversion_stage2_features_data (Child Table)
```sql
CREATE TABLE IF NOT EXISTS public.qm_conversion_stage2_features_data (
    feature_id BIGSERIAL PRIMARY KEY,
    stage2_id BIGINT,
    feature_name VARCHAR(100),
    feature_keywords VARCHAR(255),
    object_path VARCHAR(255),
    predecessor VARCHAR(100),
    created_dt TIMESTAMP DEFAULT NOW(),
    CONSTRAINT fk_stage2_features_stage2_id 
        FOREIGN KEY (stage2_id) 
        REFERENCES public.qm_conversion_stage2(stage2_id) 
        ON DELETE CASCADE
);
```

3. **Get approved statements** (Query database for statements where `is_manually_deployed = true`)

### Output
```
List of Approved Statements: [Statement5, Statement10, Statement13]
```

---

## Step 2: Identify Available Features

### Purpose
Map each approved statement to its feature combinations using stored metadata.

### Process
- For each statement from Step 1
- Find the source statement
- Look up feature combinations from Post-Stage 1 Processing metadata
- Create complete mapping

### Output Format
```python
List of Available Feature Statements:
[
    (Statement5, [
        {
            'feature_name': 'date_conversion',
            'feature_keywords': 'DATE_FUNC',
            'object_path': 'modules/date_utils.py',
            'predecessor': 'predecessor1'
        },
        {
            'feature_name': 'null_handling',
            'feature_keywords': 'NULL_CHECK',
            'object_path': 'modules/null_handler.py',
            'predecessor': 'predecessor2'
        }
    ]),
    (Statement10, [
        {
            'feature_name': 'numeric_functions',
            'feature_keywords': 'NUMERIC_CONV',
            'object_path': 'modules/numeric_utils.py',
            'predecessor': 'predecessor3'
        }
    ]),
    (Statement13, [
        {
            'feature_name': 'date_conversion',
            'feature_keywords': 'DATE_FUNC',
            'object_path': 'modules/date_utils.py',
            'predecessor': 'predecessor4'
        },
        {
            'feature_name': 'case_handling',
            'feature_keywords': 'CASE_STMT',
            'object_path': 'modules/case_handler.py',
            'predecessor': 'predecessor5'
        }
    ])
]
```

---

## Step 3: Identify Responsible Features

### Purpose
From all available features, identify which ones are actually causing deployment errors.

### Process
For each statement, analyze:
- Source statement (original input)
- QMigrator output (target_statement) - current incorrect output
- AI corrected output (converted_statement) - desired correct output
- Available feature combinations (from Step 2)
- Deployment error details
- Python module code for each feature

Filter from "available features" to "responsible features".

### Output Format
```python
List of Responsible Features Statements:
[
    (Statement5, [
        {
            'feature_name': 'date_conversion',
            'feature_keywords': 'DATE_FUNC',
            'object_path': 'modules/date_utils.py',
            'predecessor': 'predecessor1'
        }
    ]),
    (Statement10, [
        {
            'feature_name': 'numeric_functions',
            'feature_keywords': 'NUMERIC_CONV',
            'object_path': 'modules/numeric_utils.py',
            'predecessor': None
        }
    ]),
    (Statement13, [
        {
            'feature_name': 'case_handling',
            'feature_keywords': 'CASE_STMT',
            'object_path': 'modules/case_handler.py',
            'predecessor': None
        }
    ])
]
```

---

## Step 4: Validate Responsible Features

### Purpose
Verify that identified responsible features are truly responsible for conversion gaps.

### Process
For each responsible feature from Step 3, analyze:
- Responsible feature metadata
- Source statement (original input)
- QMigrator output (target_statement) - current incorrect output
- AI corrected output (converted_statement) - desired correct output
- Python module code at object_path
- Deployment error details
- Feature dependencies and conflicts
- Module update feasibility

### Output Format
```json
{
    "validation_result": "success" | "failed",
    "analysis": "For Statement5: date_conversion module exists at modules/date_utils.py and handles TO_DATE patterns but currently converts TO_DATE to TO_TIMESTAMP instead of ::DATE syntax. Module is confirmed responsible for conversion gap and can be safely updated."
}
```

### Flow Logic
- If `validation_result == "success"` → Proceed to Generate Module Update
- If `validation_result == "failed"` → Return to Identify Responsible Features

---

## Step 5: Generate Module Update

### Purpose
Update responsible Python modules to fix conversion gaps without disturbing original modules.

### Process
For each validated responsible feature (one feature = one module):

#### Module Update Process
1. **Read Original Module:**
   ```
   Source: qbookv2/Conversion_Modules/{migration_name}/{Common/Pre}/{module_name}.py
   ```

2. **Analyze Current Logic:** Parse existing conversion rules and patterns

3. **Generate Updated Logic:** Create new conversion rules based on source → converted_statement mapping

4. **Apply Updates:** Modify module code with new/updated conversion logic

5. **Save Updated Module:**
   ```
   Destination: qbookv2/Conversion_Modules_Updated/{migration_name}/{Common/Pre}/{module_name}.py
   ```

### Output Format
```json
{
    "update_result": "success" | "failed",
    "updated_module": {
        "feature_name": "date_conversion",
        "original_path": "qbookv2/Conversion_Modules/Oracle_Postgres14/Common/modules/date_utils.py",
        "updated_path": "qbookv2/Conversion_Modules_Updated/Oracle_Postgres14/Common/modules/date_utils.py",
        "changes_applied": "Updated TO_DATE conversion logic from TO_TIMESTAMP to ::DATE syntax",
        "update_status": "success"
    },
    "analysis": "Successfully updated date_conversion module for Statement5. Module updated with new TO_DATE → ::DATE conversion logic and saved to staging area."
}
```

### Flow Logic
- If `update_result == "success"` → Proceed to Validate Module Code Quality
- If `update_result == "failed"` → Retry Generate Module Update

---

## Step 6: Validate Module Code Quality

### Purpose
Use AI to validate that updated Python module code is correctly implemented.

### Process
For the updated module from Step 5, validate using AI analysis:
- Updated module code (from Conversion_Modules_Updated)
- Original module code (for comparison)
- Source statement (original input)
- Expected converted statement (AI corrected output)
- Feature metadata
- Conversion requirements

### AI Code Quality Validation Process
1. **Read Updated Module Code:** Load and parse structure
2. **Read Original Module Code:** Load for comparison
3. **AI Code Analysis:**
   - **Syntax Validation:** Check for Python syntax errors
   - **Logic Validation:** Verify conversion logic is correct
   - **Pattern Matching:** Ensure code handles source pattern correctly
   - **Output Validation:** Confirm code will produce expected conversion
   - **Integration Check:** Verify code follows QMigrator standards
4. **Requirement Mapping:** Verify code addresses conversion gap

### Example Analysis
```
Source: "SELECT TO_DATE('2023-01-01', 'YYYY-MM-DD') FROM dual"
Expected: "SELECT '2023-01-01'::DATE FROM dual"

AI Analysis Results:
✓ Code syntax is valid
✓ Pattern matching logic identifies TO_DATE correctly
✓ Conversion logic replaces TO_DATE with ::DATE syntax
✓ Code follows QMigrator module structure
✓ Will produce expected output for given input
```

### Output Format
```json
{
    "validation_result": "success" | "failed",
    "analysis": "Updated date_conversion module code is syntactically correct and implements proper TO_DATE to ::DATE conversion logic. Code will produce expected output and follows QMigrator standards."
}
```

### Flow Logic
- If `validation_result == "success"` → Proceed to Apply Module Update
- If `validation_result == "failed"` → Return to Generate Module Update

---

## Step 7: Apply Module Update

### Purpose
Apply validated module update to staging area and prepare for QMigrator testing.

### Process
For the validated module from Step 6:
- Updated module path (from Conversion_Modules_Updated)
- Feature metadata
- Module deployment status
- QMigrator configuration

### Apply Module Process
1. **Confirm Module Location:** Verify updated module exists in staging area
   ```
   qbookv2/Conversion_Modules_Updated/{migration_name}/{Common/Pre-dynamic}/{module_name}.py
   ```
2. **Update Module Registry:** Register updated module for QMigrator use
3. **Prepare Module Access:** Ensure module is accessible for statement-level conversion
4. **Validate Module Integrity:** Final check that module is properly deployed

### Output Format
```json
{
    "apply_result": "success" | "failed",
    "analysis": "date_conversion module successfully applied to staging area at qbookv2/Conversion_Modules_Updated/Oracle_Postgres14/Common/modules/date_utils.py. Module ready for QMigrator statement-level testing."
}
```

### Flow Logic
- If `apply_result == "success"` → Check More Features?
- If `apply_result == "failed"` → Return to Generate Module Update

---

## Step 8: Test QMigrator Statement-Level Conversion

### Purpose
Test complete statement conversion using QMigrator with ALL qbook modules (original + updated) and use AI to validate the output against AI corrected statement.

### When
Only after all features for current statement are updated (More Features? = No)

### Process
For the current statement after all features updated, test using:
- Source statement (original input)
- AI corrected statement (expected output)
- Original qbook modules (unchanged modules from Conversion_Modules)
- Updated modules (newly updated modules from Conversion_Modules_Updated)
- QMigrator conversion engine (configured to use both module sets)
- AI validation (to compare QMigrator output vs expected output)

### QMigrator Statement Testing Process

#### Module Selection Process
For each required feature/module:
1. **Check if updated module exists:**
   ```
   qbookv2/Conversion_Modules_Updated/{migration_name}/{Common/Pre}/{module_name}.py
   ```
2. **If EXISTS:** Use updated module
3. **If NOT EXISTS:** Use original module from:
   ```
   qbookv2/Conversion_Modules/{migration_name}/{Common/Pre}/{module_name}.py
   ```

#### Configure QMigrator with Selected Modules
- Load updated modules for features that were processed
- Load original modules for features that were not processed
- Create module configuration for QMigrator

#### Run Complete Statement Conversion
Process entire source statement using selected module set

#### Generate QMigrator Output
Get complete converted statement

#### AI Validation Process
- **Input:** QMigrator output vs AI corrected statement
- **AI Analysis:** Compare both outputs for correctness, syntax, logic equivalence
- **Validation:** Determine if QMigrator output matches expected AI corrected statement

### Example Module Selection
```
Statement features needed: [date_conversion, null_handling, case_handling]

Module Selection Logic:
1. Check date_conversion:
   - Updated exists? YES → Use Conversion_Modules_Updated/.../date_utils.py
2. Check null_handling:
   - Updated exists? YES → Use Conversion_Modules_Updated/.../null_handler.py
3. Check case_handling:
   - Updated exists? NO → Use Conversion_Modules/.../case_handler.py

Final QMigrator Configuration:
- date_conversion: Updated module
- null_handling: Updated module
- case_handling: Original module
```

### Output Format
```json
{
    "validation_result": "success" | "failed",
    "analysis": "AI validation confirms QMigrator statement-level conversion using selective modules (2 updated: date_conversion, null_handling; 1 original: case_handling) produces output that exactly matches the AI corrected statement. All features working together correctly."
}
```

### Flow Logic

#### If validation_result == "success" → Check More Statements?
```
Step 8 (Success) → More Statements? Decision Node
├── If YES (More Statements) → Go to Step 3: Identify-Responsible-Features
│   └── Process next statement with its features
├── If NO (No More Statements) → END
│   └── **WORKFLOW COMPLETE** ✅
```

#### If validation_result == "failed" → Return to Generate Module Update
```
Step 8 (Failed) → Return to Step 5: Generate-Module-Update
└── Retry the problematic features for current statement
    └── Step 5 → Step 6 → Step 7 → Step 8 (retry loop)
```

---

## Workflow Completion

### Success Criteria
The Stage 2 workflow completes successfully when:
- ✅ All approved statements have been processed
- ✅ All responsible features have been identified and updated
- ✅ All QMigrator modules have been validated and applied
- ✅ Statement-level conversions match AI corrected outputs
- ✅ No more statements remain for processing

### Final Result
**QMigrator modules are updated to produce correct conversions without requiring AI intervention.**

### Key Benefits
- **Automated Module Updates:** QMigrator modules self-improve based on AI corrections
- **Reduced Manual Intervention:** Future conversions work correctly without AI fixes
- **Safe Module Management:** Original modules preserved, updates staged separately
- **Comprehensive Validation:** Multiple validation checkpoints ensure quality
- **Scalable Process:** Handles multiple statements and features systematically

---

## Technical Architecture

### Module Storage Strategy
```
Azure Blob Storage (qbookv2):
├── Conversion_Modules/                    # Original modules (read-only)
│   └── {migration_name}/
│       └── {Common/Pre-dynamic}/
│           └── modules/
└── Conversion_Modules_Updated/            # Updated modules (staging)
    └── {migration_name}/
        └── {Common/Pre-dynamic}/
            └── modules/
```

### Processing Flow
- **Statement-by-Statement:** Each statement processed individually
- **Feature-by-Feature:** Within each statement, features processed sequentially
- **Module-by-Module:** One feature updates one module (1:1 relationship)
- **Validation at Every Step:** Multiple checkpoints ensure quality and safety

### Retry Mechanisms
- **Step 4 Failure:** Return to Step 3 (Identify Responsible Features)
- **Step 6 Failure:** Return to Step 5 (Generate Module Update)
- **Step 7 Failure:** Return to Step 5 (Generate Module Update)
- **Step 8 Failure:** Return to Step 5 (Generate Module Update)

This comprehensive workflow ensures reliable, safe, and effective updating of QMigrator Python modules based on AI corrections from Stage 1.

---

## Step 9: Human Review Interface

### Purpose
Present all updated modules to the user for manual approval/rejection before final deployment.

### Process
Once all statements have been processed and modules updated, display comprehensive metadata for user review:

### Human Review Display Format
```
Updated Modules Summary for Review:

Statement 5:
├── Feature1 (date_conversion):
│   ├── [Old Module] modules/date_utils.py (TO_DATE → TO_TIMESTAMP)
│   ├── [Updated Module] modules/date_utils.py (TO_DATE → ::DATE)
│   └── [Action] [Approve] [Reject]

Statement 10:
├── Feature2 (numeric_functions):
│   ├── [Old Module] modules/numeric_utils.py (NUMERIC_CONV logic)
│   ├── [Updated Module] modules/numeric_utils.py (Enhanced NUMERIC_CONV)
│   └── [Action] [Approve] [Reject]

Statement 13:
├── Feature3 (case_handling):
│   ├── [Old Module] modules/case_handler.py (Basic CASE logic)
│   ├── [Updated Module] modules/case_handler.py (Advanced CASE logic)
│   └── [Action] [Approve] [Reject]

[Perform Object Conversion] [Cancel All Updates]
```

### Review Interface Features
- **Side-by-Side Comparison:** Show old vs updated module code
- **Change Highlights:** Highlight specific changes made to each module
- **Test Results:** Display validation results for each module
- **Batch Actions:** Allow approve/reject all or selective approval
- **Module Dependencies:** Show which modules depend on others

### User Actions
- **Individual Approval:** Approve/reject each module separately
- **Bulk Actions:** Approve all, reject all, or selective approval
- **Code Review:** View detailed code changes before approval
- **Test Validation:** Review test results and AI validation reports

### Output
```json
{
    "review_status": "completed",
    "approved_modules": [
        {
            "feature_name": "date_conversion",
            "module_path": "modules/date_utils.py",
            "approval_status": "approved",
            "approved_by": "user_id",
            "approval_timestamp": "2024-01-15T10:30:00Z"
        }
    ],
    "rejected_modules": [
        {
            "feature_name": "numeric_functions",
            "module_path": "modules/numeric_utils.py",
            "rejection_status": "rejected",
            "rejection_reason": "Logic too complex, needs simplification",
            "rejected_by": "user_id",
            "rejection_timestamp": "2024-01-15T10:32:00Z"
        }
    ],
    "pending_modules": []
}
```

---

## Step 10: Object Conversion Execution

### Purpose
Execute final object-level conversion using only approved modules and update system metadata.

### Process

#### User Action
User clicks **"Perform Object Conversion"** button after reviewing and approving modules.

#### Execution Process
1. **Apply Approved Modules Only:**
   - Use only modules marked as "approved" in the review interface
   - Ignore rejected modules (keep original versions)
   - Configure QMigrator with approved module set

2. **Run Object-Level Conversion:**
   - Execute complete object conversion using approved updated modules
   - Process entire source object with new module configuration
   - Generate final converted object code

3. **Update System Metadata:**
   - Update `tgt_objects` table with conversion results
   - Update `tgt_deploy_status` table with deployment status
   - Record module update history and approval details

### Outcome Handling

#### Success Scenario
```json
{
    "conversion_status": "success",
    "converted_object": "complete_converted_code",
    "modules_applied": ["date_conversion", "case_handling"],
    "modules_rejected": ["numeric_functions"],
    "next_action": "merge_to_qbook"
}
```

**Actions on Success:**
- **Merge Updated Modules into Qbook:** Move approved modules from staging to production
- **Update Production Modules:** Replace original modules with approved updated versions
- **Archive Original Modules:** Keep backup of original modules for rollback
- **Update Conversion History:** Record successful module updates

#### Failure Scenario
```json
{
    "conversion_status": "failed",
    "error_details": "Object conversion failed with approved modules",
    "failed_modules": ["date_conversion"],
    "error_message": "Syntax error in updated date_conversion module",
    "next_action": "trigger_stage1"
}
```

**Actions on Failure:**
- **Trigger "Perform Stage 1" Button:** Enable re-conversion option
- **Rollback to Original Modules:** Revert to original module versions
- **Log Failure Details:** Record failure reasons and module issues
- **Notify User:** Display error message and suggested actions

### Final System Updates

#### Database Updates on Success
```sql
-- Update target objects table
UPDATE tgt_objects
SET converted_code = ?,
    conversion_status = 'completed',
    modules_updated = ?,
    updated_timestamp = NOW()
WHERE tgt_object_id = ?;

-- Update deployment status
UPDATE tgt_deploy_status
SET deployment_status = 'ready_for_deployment',
    stage2_completed = true,
    approved_modules = ?,
    completion_timestamp = NOW()
WHERE tgt_object_id = ?;
```

#### Module Management
```
Production Module Update:
qbookv2/Conversion_Modules/{migration_name}/{Common/Pre}/modules/
├── date_utils.py (updated - approved)
├── case_handler.py (updated - approved)
└── numeric_utils.py (original - rejected)

Archive:
qbookv2/Conversion_Modules_Archive/{migration_name}/v1.0/
├── date_utils.py (original backup)
└── case_handler.py (original backup)
```

### User Interface Flow
```
Stage 2 Complete → Human Review → Module Approval → Object Conversion
                                      ↓
                              [Success] → Merge to Qbook → Complete
                                      ↓
                              [Failure] → Enable Stage 1 → Retry
```

This completes the end-to-end Stage 2 workflow with human oversight and final deployment control.
