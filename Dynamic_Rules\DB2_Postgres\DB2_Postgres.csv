Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
DB2_Postgres,Start: Create or replace function End:is|as&Start: Create function End:is|as&Start:as|is End:;&Start:;+1 End:;&Start: Create or replace editionable function End:is|as,Start: /* End: */&Start: -- End: \n,,Function/Statement,ObjectTypes object (202)
DB2_Postgres,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (982)
DB2_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (997)
DB2_Postgres,Start: Create or replace trigger End:Object-1&Start: Create or replace editionable trigger End:Object-1&Start: Create trigger End:Object-1,Start:/* End:*/&Start:-- End:\n,,Trigger,ObjectTypes object (208)
DB2_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (985)
DB2_Postgres,Start: Create sequence End:Object-1&Start: Create sequence End:;,,,Sequence,ObjectTypes object (1000)
DB2_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (988)
DB2_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (1003)
DB2_Postgres,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (1006)
DB2_Postgres,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (991)
DB2_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (994)
DB2_Postgres,Start: Create or replace procedure End:Object-1&Start: Create or replace editionable procedure End:Object-1&Start: Create procedure End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (190)
DB2_Postgres,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (193)
DB2_Postgres,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (187)
DB2_Postgres,Start: Create or replace procedure End:is|as&Start: Create procedure End:is|as&Start:as|is End:;&Start:;+1 End:;&Start:select End:;&Start: Create or replace editionable procedure End:is|as,Start: /* End: */&Start: -- End: \n,,Procedure/Statement,ObjectTypes object (199)
