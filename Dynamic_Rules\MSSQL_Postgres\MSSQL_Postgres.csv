Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (256)
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (997)
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (166)
MSSQL_Postgres,Start:create table End:object-1&Start:create global temporary table End:;&Start:create table End:;,Start:/* End:*/&Start:-- End:\n,,Temporary_Table,ObjectTypes object (271)
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (994)
MSSQL_Postgres,Start: Create sequence End:Object-1&Start: Create sequence End:;,,,Sequence,ObjectTypes object (1000)
MSSQL_Postgres,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (247)
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (985)
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (1003)
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (250)
MSSQL_Postgres,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (988)
MSSQL_Postgres,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (1006)
MSSQL_Postgres,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (253)
MSSQL_Postgres,Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1&Start:Create unique index End:;,,,Index,ObjectTypes object (991)
MSSQL_Postgres,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (982)
MSSQL_Postgres,Start: Create or replace function End:is|as&Start: Create or editionable function End:is|as&Start: Create function End:is|as&Start:as|is End:;&Start:;+1 End:;,Start: /* End: */&Start: -- End: \n,,Function/Statement,ObjectTypes object (157)
MSSQL_Postgres,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (154)
MSSQL_Postgres,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,,,Table,ObjectTypes object (151)
MSSQL_Postgres,Start: Create or replace procedure End:Object-1&Start: Create or replace editionable procedure End:Object-1&Start: Create procedure End:Object-1&Start: Create or alter procedure End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (61)
MSSQL_Postgres,Start: Create or replace procedure End:is|as&Start: Create or editionable procedure End:is|as&Start: Create procedure End:is|as&Start:as|is End:;&Start:;+1 End:;&Start:select End:;&Start: Create or alter procedure End:is|as,Start: /* End: */&Start: -- End: \n,,Procedure/Statement,ObjectTypes object (64)
