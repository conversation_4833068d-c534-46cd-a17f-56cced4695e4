from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from routes import dba_route, conversion_route, conversion_route_stage2, performance_route
from auth import JWTMiddleware
import uvicorn

# Create HTTPBearer security scheme for Swagger UI
security = HTTPBearer()

app = FastAPI(
    title="QMigrator Web Agents",
    description="AI API's for QMigrator",
    docs_url="/ai/docs",
    redoc_url="/ai/redoc",
    openapi_url="/ai/openapi.json"
)

# Configure OpenAPI security for Swagger UI
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema

    from fastapi.openapi.utils import get_openapi

    openapi_schema = get_openapi(
        title=app.title,
        version="1.0.0",
        description=app.description,
        routes=app.routes,
    )

    # Add Bearer token security scheme
    openapi_schema["components"]["securitySchemes"] = {
        "BearerAuth": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT"
        }
    }

    # Apply security to all paths except public routes
    for path, path_item in openapi_schema["paths"].items():
        # Skip public routes
        if any(public_path in path for public_path in ["/docs", "/openapi.json", "/redoc"]):
            continue

        # Add security requirement to all operations
        for operation in path_item.values():
            if isinstance(operation, dict) and "operationId" in operation:
                operation["security"] = [{"BearerAuth": []}]

    app.openapi_schema = openapi_schema
    return app.openapi_schema

app.openapi = custom_openapi


API_PREFIX_URL = "ai"

# Add JWT Authentication Middleware
app.add_middleware(JWTMiddleware)

# Add CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "*",
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"]
)

app.include_router(dba_route.router, prefix=f"/{API_PREFIX_URL}/dba", tags=["DBA"])
app.include_router(performance_route.router, prefix=f"/{API_PREFIX_URL}/performance", tags=["Performance Tuning"])
app.include_router(conversion_route.router, prefix=f"/{API_PREFIX_URL}/conversion", tags=["Migration"])
app.include_router(conversion_route_stage2.router, prefix=f"/{API_PREFIX_URL}/conversion_stage2", tags=["Migration Stage 2"])


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)