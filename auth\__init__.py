"""
Authentication module for QMigrator Web Agents.

This module provides JWT-based authentication middleware for FastAPI applications.
"""

from .jwt_middleware import JWTMiddleware, verify_jwt_token
from .jwt_config import JWTConfig
from .utils import (
    get_user_from_request,
    get_user_id_from_request,
    get_project_id_from_request,
    is_authenticated
)

__all__ = [
    "JWTMiddleware",
    "JWTConfig",
    "verify_jwt_token",
    "get_user_from_request",
    "get_user_id_from_request",
    "get_project_id_from_request",
    "is_authenticated"
]
