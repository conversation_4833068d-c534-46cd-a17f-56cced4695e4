"""
Authentication Testing Script for QMigrator Web Agents.

This script helps test the JWT authentication implementation by generating
test tokens and making sample requests.
"""

import requests
import jwt
from datetime import datetime, timedelta
from config import Config


def generate_test_token(user_id: str = "test_user", project_id: int = 1, expires_in_hours: int = 24) -> str:
    """Generate a test JWT token."""
    payload = {
        "user_id": user_id,
        "project_id": project_id,
        "exp": datetime.utcnow() + timedelta(hours=expires_in_hours),
        "iat": datetime.utcnow()
    }
    
    token = jwt.encode(
        payload,
        Config.JWT_SECRET_KEY,
        algorithm=Config.JWT_ALGORITHM
    )
    
    return token


def test_authentication(base_url: str = "http://localhost:8000"):
    """
    Test authentication by making requests to the API.
    
    Args:
        base_url (str): Base URL of your FastAPI application
    """
    
    print("=== QMigrator Web Agents Authentication Test ===\n")
    
    # Test 1: Access public route (should work without token)
    print("1. Testing public route access (no authentication required)...")
    try:
        response = requests.get(f"{base_url}/ai/docs")
        print(f"   ✅ Public route accessible: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Public route failed: {e}")
    
    # Test 2: Access protected route without token (should fail)
    print("\n2. Testing protected route without token (should fail)...")
    try:
        response = requests.get(f"{base_url}/ai/dba/dba-agent")
        print(f"   Status: {response.status_code}")
        if response.status_code == 401:
            print("   ✅ Correctly rejected request without token")
        else:
            print("   ❌ Should have returned 401 Unauthorized")
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test 3: Access protected route with invalid token (should fail)
    print("\n3. Testing protected route with invalid token (should fail)...")
    try:
        headers = {"Authorization": "Bearer invalid.jwt.token"}
        response = requests.get(f"{base_url}/ai/dba/dba-agent", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code == 401:
            print("   ✅ Correctly rejected invalid token")
        else:
            print("   ❌ Should have returned 401 Unauthorized")
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test 4: Access protected route with valid token (should work)
    print("\n4. Testing protected route with valid token (should work)...")
    try:
        valid_token = generate_test_token()
        headers = {"Authorization": f"Bearer {valid_token}"}
        
        # Note: This will still fail because the route expects form data,
        # but it should fail with a different error (422 or 400) not 401
        response = requests.get(f"{base_url}/ai/dba/dba-agent", headers=headers)
        print(f"   Status: {response.status_code}")
        if response.status_code != 401:
            print("   ✅ Token accepted (route may require different method/data)")
        else:
            print("   ❌ Valid token was rejected")
    except Exception as e:
        print(f"   ❌ Request failed: {e}")
    
    # Test 5: Generate and display test token
    print("\n5. Generated test token for manual testing:")
    test_token = generate_test_token(user_id="test_user", project_id=123)
    print(f"   Token: {test_token}")
    print(f"   Use this in Authorization header: Bearer {test_token}")
    
    # Decode and display token contents
    try:
        decoded = jwt.decode(test_token, Config.JWT_SECRET_KEY, algorithms=[Config.JWT_ALGORITHM])
        print(f"   Token contents: {decoded}")
    except Exception as e:
        print(f"   ❌ Failed to decode token: {e}")


def test_curl_commands():
    """Generate curl commands for manual testing."""
    print("\n=== Curl Commands for Manual Testing ===\n")
    
    # Generate test token
    test_token = generate_test_token(user_id="test_user", project_id=123)
    
    print("1. Test public route (should work):")
    print('   curl -X GET "http://localhost:8000/ai/docs"')
    
    print("\n2. Test protected route without token (should return 401):")
    print('   curl -X POST "http://localhost:8000/ai/dba/dba-agent"')
    
    print("\n3. Test protected route with invalid token (should return 401):")
    print('   curl -X POST "http://localhost:8000/ai/dba/dba-agent" \\')
    print('        -H "Authorization: Bearer invalid.token"')
    
    print("\n4. Test protected route with valid token:")
    print(f'   curl -X POST "http://localhost:8000/ai/dba/dba-agent" \\')
    print(f'        -H "Authorization: Bearer {test_token}" \\')
    print('        -H "Content-Type: application/json" \\')
    print('        -d \'{"query": "test query", "target_connection_id": 1}\'')


if __name__ == "__main__":
    print("Choose test option:")
    print("1. Run automated tests")
    print("2. Generate curl commands")
    print("3. Both")
    
    choice = input("Enter choice (1-3): ").strip()
    
    if choice in ["1", "3"]:
        test_authentication()
    
    if choice in ["2", "3"]:
        test_curl_commands()
